'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { StudentInfo, CommentGenerationResponse, User } from '@/types';

export default function Home() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [generatedComment, setGeneratedComment] = useState('');
  const [tokensUsed, setTokensUsed] = useState(0);

  // 学生信息表单状态
  const [studentInfo, setStudentInfo] = useState<StudentInfo>({
    studentName: '',
    studentGender: '男',
    termYear: '2024-2025学年第一学期',
    majorSubjectPerformance: '',
    subjectStrengths: '',
    subjectWeaknesses: '',
    learningPotential: '学习能力稳定',
    subjectInterest: '一般',
    classroomConcentration: '大部分时间专注',
    homeworkCompletion: '大部分按时完成',
    learningProactiveness: '完成要求任务',
    disciplineCompliance: '基本遵守',
    attitudeTowardsOthers: '基本有礼貌',
    responsibility: '能完成分配任务',
    talentsAndInterests: '',
    classPosition: '无职位',
    awards: '',
    overallAssessment: '',
    futureExpectations: '',
    improvementSuggestions: '',
    commentPerspective: '你',
    commentTone: '温和亲切',
    wordCountRange: '200-300字'
  });

  // 检查用户登录状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (!token || !userData) {
      router.push('/login');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/login');
    }
  }, [router]);

  // 检查是否有预填充的学生信息
  useEffect(() => {
    const prefillData = localStorage.getItem('prefillStudentInfo');
    if (prefillData) {
      try {
        const parsedData = JSON.parse(prefillData);
        setStudentInfo(parsedData);
        localStorage.removeItem('prefillStudentInfo'); // 使用后清除
      } catch (error) {
        console.error('解析预填充数据失败:', error);
      }
    }
  }, []);

  // 处理表单输入变化
  const handleInputChange = (field: keyof StudentInfo, value: string) => {
    setStudentInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 生成评语
  const generateComment = async () => {
    if (!studentInfo.studentName.trim()) {
      alert('请输入学生姓名');
      return;
    }

    setLoading(true);
    setGeneratedComment('');
    setTokensUsed(0);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/generate-comment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ studentInfo })
      });

      const data: CommentGenerationResponse = await response.json();

      if (data.success) {
        setGeneratedComment(data.comment || '');
        setTokensUsed(data.tokensUsed || 0);
      } else {
        alert(data.message || '生成评语失败');
      }
    } catch (error) {
      console.error('生成评语错误:', error);
      alert('生成评语失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 退出登录
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Easy Comments 学生评语生成系统</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">欢迎，{user.username}</span>
              <button
                onClick={() => router.push('/history')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                历史评语
              </button>
              {user.is_admin && (
                <button
                  onClick={() => router.push('/admin')}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  管理后台
                </button>
              )}
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：学生信息表单 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">学生信息录入</h2>

            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生姓名 *
                  </label>
                  <input
                    type="text"
                    value={studentInfo.studentName}
                    onChange={(e) => handleInputChange('studentName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入学生姓名"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学生性别
                  </label>
                  <select
                    value={studentInfo.studentGender}
                    onChange={(e) => handleInputChange('studentGender', e.target.value as '男' | '女')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="男">男</option>
                    <option value="女">女</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  学期/学年
                </label>
                <select
                  value={studentInfo.termYear}
                  onChange={(e) => handleInputChange('termYear', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="2023-2024学年第一学期">2023-2024学年第一学期</option>
                  <option value="2023-2024学年第二学期">2023-2024学年第二学期</option>
                  <option value="2024-2025学年第一学期">2024-2025学年第一学期</option>
                  <option value="2024-2025学年第二学期">2024-2025学年第二学期</option>
                </select>
              </div>

              {/* 学业成绩与发展 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  主要学科成绩
                </label>
                <textarea
                  value={studentInfo.majorSubjectPerformance}
                  onChange={(e) => handleInputChange('majorSubjectPerformance', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：语文:85分,班级前列; 数学:中等,进步明显"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学科强项分析
                  </label>
                  <textarea
                    value={studentInfo.subjectStrengths}
                    onChange={(e) => handleInputChange('subjectStrengths', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="学生在哪些学科表现突出"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学科薄弱点分析
                  </label>
                  <textarea
                    value={studentInfo.subjectWeaknesses}
                    onChange={(e) => handleInputChange('subjectWeaknesses', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="学生在哪些学科需要改进"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学习潜力评估
                  </label>
                  <select
                    value={studentInfo.learningPotential}
                    onChange={(e) => handleInputChange('learningPotential', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="潜力较大">潜力较大</option>
                    <option value="学习能力稳定">学习能力稳定</option>
                    <option value="需要进一步激发潜力">需要进一步激发潜力</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    对学科的兴趣程度
                  </label>
                  <select
                    value={studentInfo.subjectInterest}
                    onChange={(e) => handleInputChange('subjectInterest', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="浓厚">浓厚</option>
                    <option value="一般">一般</option>
                    <option value="缺乏兴趣">缺乏兴趣</option>
                  </select>
                </div>
              </div>

              {/* 课堂表现与参与 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  课堂专注度
                </label>
                <select
                  value={studentInfo.classroomConcentration}
                  onChange={(e) => handleInputChange('classroomConcentration', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="始终高度专注">始终高度专注</option>
                  <option value="大部分时间专注">大部分时间专注</option>
                  <option value="有时分散注意力">有时分散注意力</option>
                  <option value="容易受外界干扰">容易受外界干扰</option>
                </select>
              </div>

              {/* 行为习惯与态度 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    作业完成情况
                  </label>
                  <select
                    value={studentInfo.homeworkCompletion}
                    onChange={(e) => handleInputChange('homeworkCompletion', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="总是按时高质量">总是按时高质量</option>
                    <option value="大部分按时完成">大部分按时完成</option>
                    <option value="有时拖延或应付">有时拖延或应付</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    学习主动性与自觉性
                  </label>
                  <select
                    value={studentInfo.learningProactiveness}
                    onChange={(e) => handleInputChange('learningProactiveness', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="学习主动性强">学习主动性强</option>
                    <option value="完成要求任务">完成要求任务</option>
                    <option value="需要督促">需要督促</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    遵守纪律与规章
                  </label>
                  <select
                    value={studentInfo.disciplineCompliance}
                    onChange={(e) => handleInputChange('disciplineCompliance', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="严格遵守">严格遵守</option>
                    <option value="基本遵守">基本遵守</option>
                    <option value="需要加强">需要加强</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    待人接物态度
                  </label>
                  <select
                    value={studentInfo.attitudeTowardsOthers}
                    onChange={(e) => handleInputChange('attitudeTowardsOthers', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="有礼貌尊重他人">有礼貌尊重他人</option>
                    <option value="基本有礼貌">基本有礼貌</option>
                    <option value="不够尊重">不够尊重</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    责任心
                  </label>
                  <select
                    value={studentInfo.responsibility}
                    onChange={(e) => handleInputChange('responsibility', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="较强">较强</option>
                    <option value="能完成分配任务">能完成分配任务</option>
                    <option value="有待提高">有待提高</option>
                  </select>
                </div>
              </div>

              {/* 个性特长与潜能 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  特长与兴趣
                </label>
                <textarea
                  value={studentInfo.talentsAndInterests}
                  onChange={(e) => handleInputChange('talentsAndInterests', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：爱好绘画,作品有创意; 篮球特长,是校队成员"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    班干职位
                  </label>
                  <select
                    value={studentInfo.classPosition}
                    onChange={(e) => handleInputChange('classPosition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="无职位">无职位</option>
                    <option value="班长">班长</option>
                    <option value="副班长">副班长</option>
                    <option value="学习委员">学习委员</option>
                    <option value="体育委员">体育委员</option>
                    <option value="文艺委员">文艺委员</option>
                    <option value="生活委员">生活委员</option>
                    <option value="纪律委员">纪律委员</option>
                    <option value="宣传委员">宣传委员</option>
                    <option value="组织委员">组织委员</option>
                    <option value="科代表">科代表</option>
                    <option value="小组长">小组长</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    获奖情况
                  </label>
                  <textarea
                    value={studentInfo.awards}
                    onChange={(e) => handleInputChange('awards', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="例如：校三好学生、数学竞赛二等奖、优秀班干部等"
                  />
                </div>
              </div>

              {/* 教师期望与个性化建议 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  教师总体评价
                </label>
                <textarea
                  value={studentInfo.overallAssessment}
                  onChange={(e) => handleInputChange('overallAssessment', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="教师对学生该阶段表现的总体概括性评价"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    未来发展期望
                  </label>
                  <textarea
                    value={studentInfo.futureExpectations}
                    onChange={(e) => handleInputChange('futureExpectations', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="教师希望学生在未来重点发展的方向"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    针对性改进建议
                  </label>
                  <textarea
                    value={studentInfo.improvementSuggestions}
                    onChange={(e) => handleInputChange('improvementSuggestions', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="具体的改进措施或努力方向"
                  />
                </div>
              </div>

              {/* 评语设置 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评语人称
                  </label>
                  <select
                    value={studentInfo.commentPerspective}
                    onChange={(e) => handleInputChange('commentPerspective', e.target.value as '你' | '该生')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="你">你</option>
                    <option value="该生">该生</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评语语气
                  </label>
                  <select
                    value={studentInfo.commentTone}
                    onChange={(e) => handleInputChange('commentTone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="温和亲切">温和亲切</option>
                    <option value="严谨正式">严谨正式</option>
                    <option value="鼓励激励">鼓励激励</option>
                    <option value="客观中性">客观中性</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评语字数范围
                  </label>
                  <select
                    value={studentInfo.wordCountRange}
                    onChange={(e) => handleInputChange('wordCountRange', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="150-200字">150-200字</option>
                    <option value="200-300字">200-300字</option>
                    <option value="300-400字">300-400字</option>
                    <option value="400-500字">400-500字</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：生成的评语 */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-medium text-gray-900">生成的评语</h2>
              <button
                onClick={generateComment}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-grey-100 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '生成中...' : '生成评语'}
              </button>
            </div>

            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">AI正在生成评语，请稍候...</span>
              </div>
            )}

            {generatedComment && (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
                    {generatedComment}
                  </div>
                </div>

                {tokensUsed > 0 && (
                  <div className="text-sm text-gray-500">
                    本次生成使用了 {tokensUsed} 个 Token
                  </div>
                )}

                <div className="flex space-x-2">
                  <button
                    onClick={() => navigator.clipboard.writeText(generatedComment)}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                  >
                    复制评语
                  </button>
                  <button
                    onClick={() => setGeneratedComment('')}
                    className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    清空
                  </button>
                </div>
              </div>
            )}

            {!generatedComment && !loading && (
              <div className="text-center py-8 text-gray-500">
                填写学生信息后点击&ldquo;生成评语&rdquo;按钮
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
